/* Facilities Gallery Slider */

/* Image Modal (Full Image View) */
.image-modal {
    position: fixed;
    z-index: 9999;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0,0,0,0.7);
    animation: modalFadeIn 0.2s;
}
.image-modal.closing {
    animation: modalFadeOut 0.2s forwards;
}
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes modalFadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
.image-modal-backdrop {
    position: absolute;
    inset: 0;
    background: transparent;
    cursor: zoom-out;
}
.image-modal-content {
    position: relative;
    z-index: 2;
    background: none;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.image-modal-content img {
    max-width: 90vw;
    max-height: 80vh;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.5);
    background: #fff;
    object-fit: contain;
    animation: modalImgIn 0.2s;
}
@keyframes modalImgIn {
    from { transform: scale(0.96); opacity: 0.7; }
    to { transform: scale(1); opacity: 1; }
}
.image-modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.6);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    font-size: 2rem;
    cursor: pointer;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}
.image-modal-close:hover {
    background: #4682b4;
    color: #fff;
}
@media (max-width: 600px) {
    .image-modal-content img {
        max-width: 98vw;
        max-height: 60vh;
    }
}

#facilities-gallery {
    padding: 80px 0;
}

.facilities-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.tab-btn {
    background: transparent;
    border: 2px solid #4682b4;
    color: #4682b4;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    font-size: 0.8rem;
    cursor: pointer;
    border-radius: 24px;
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 9px 25px;
    margin: 0 8px 16px 0;
}

.tab-btn:focus,
.tab-btn:active {
    outline: none;
    box-shadow: 0 4px 16px 0 rgba(30, 60, 114, 0.18);
    transform: scale(0.97);
    background: linear-gradient(135deg, #2a5298 0%, #4682b4 100%);
    color: #fff;
    border: 2px solid transparent;
}

.tab-btn.active {
    transform: scale(1.04);
}





.tab-btn:hover,
.tab-btn.active {
    background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
}

.facilities-slider {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(30, 60, 114, 0.1);
}

.slider-container {
    overflow: hidden;
    width: 100%;
}

.slider-track {
    display: flex;
    transition: transform 0.5s ease;
    will-change: transform;
}

.facility-slide {
    min-width: 100%;
    position: relative;
}

.facility-slide.hidden {
    display: none;
}

.facility-image {
    position: relative;
    width: 100%;
    height: 600px;
    overflow: hidden;
    border-radius: 20px;
}

.facility-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.facility-slide:hover .facility-image img {
    transform: scale(1.05);
}

.facility-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 40px;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.facility-slide:hover .facility-overlay {
    transform: translateY(0);
    opacity: 1;
}

.facility-overlay h4 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: white;
}

.facility-overlay p {
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

.slider-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.slider-btn:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

.slider-btn i {
    font-size: 1.2rem;
    color: #1e3c72;
}

.slider-dots {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 30px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(70, 130, 180, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #4682b4;
    transform: scale(1.2);
}

.dot:hover {
    background: #4682b4;
    transform: scale(1.1);
}

/* Gallery Slider - Same styles as FACILITIES */
.gallery-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.gallery-tabs .tab-btn {
    padding: 9px 25px;
    background: #fff;
    border: 2px solid #4682b4;
    color: #4682b4;
    font-family: 'Kanit', sans-serif;
    font-weight: 600;
    font-size: 0.8rem;
    cursor: pointer;
    border-radius: 24px;
    transition: all 0.3s cubic-bezier(0.4,0,0.2,1);
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: none;
    outline: none;
    margin: 0 8px 16px 0;
    position: relative;
    z-index: 1;
}



.gallery-tabs .tab-btn:hover,
.gallery-tabs .tab-btn.active {
  background: linear-gradient(135deg, #1e3c72 0%, #4682b4 100%);
  color: #fff;
  border: 2px solid transparent;
  box-shadow: 0 8px 24px 0 rgba(30, 60, 114, 0.18);
  outline: none;
  position: relative;
  z-index: 2;
}

.gallery-slider {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(30, 60, 114, 0.1);
}

.gallery-slider .slider-container {
    overflow: hidden;
    width: 100%;
    border-radius: 20px;
}

.gallery-slider .slider-track {
    display: flex;
    transition: transform 0.5s ease;
    will-change: transform;
}

.gallery-slide {
    min-width: 100%;
    position: relative;
    display: block;
    visibility: visible;
}

.gallery-slide.hidden {
    display: none !important;
}

/* FIXED: Make gallery-image exactly like facility-image */
.gallery-image {
    position: relative !important;
    width: 100% !important;
    height: 600px !important;
    overflow: hidden !important;
    border-radius: 20px !important;
    background: #f0f0f0 !important;
}

/* FIXED: Make gallery-image img exactly like facility-image img */
.gallery-image img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.3s ease !important;
    display: block !important;
    border-radius: 0 !important;
}

.gallery-slide:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 40px;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-slide:hover .gallery-overlay {
    transform: translateY(0);
    opacity: 1;
}

.gallery-overlay h4 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: white;
}

.gallery-overlay p {
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Responsive Design for Gallery */
@media (max-width: 768px) {
    .gallery-tabs {
        gap: 15px;
    }
    
    .gallery-tabs .tab-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .gallery-image {
        height: 400px;
    }
    
    .gallery-overlay {
        padding: 20px;
    }
    
    .gallery-overlay h4 {
        font-size: 1.4rem;
    }
    
    .gallery-overlay p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .gallery-image {
        height: 350px;
    }
    
    .gallery-overlay {
        padding: 15px;
    }
    
    .gallery-overlay h4 {
        font-size: 1.2rem;
    }
    
    .gallery-overlay p {
        font-size: 0.9rem;
    }
}
@media (max-width: 768px) {
    .facilities-tabs {
        gap: 15px;
    }
    
    .tab-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .facility-image {
        height: 400px;
    }
    
    .facility-overlay {
        padding: 20px;
    }
    
    .facility-overlay h4 {
        font-size: 1.4rem;
    }
    
    .facility-overlay p {
        font-size: 1rem;
    }
    
    .slider-btn {
        width: 50px;
        height: 50px;
    }
    
    .prev-btn {
        left: 10px;
    }
    
    .next-btn {
        right: 10px;
    }
}

@media (max-width: 480px) {
    .facility-image {
        height: 350px;
    }
    
    .facility-overlay {
        padding: 15px;
    }
    
    .facility-overlay h4 {
        font-size: 1.2rem;
    }
    
    .facility-overlay p {
        font-size: 0.9rem;
    }
}