import React, { useEffect } from 'react'

const ImageModal = ({ isOpen, onClose, imageSrc, imageAlt, imageTitle }) => {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div className="image-modal" onClick={handleBackdropClick}>
      <div className="image-modal-backdrop" />
      <div className="image-modal-content">
        <button 
          className="image-modal-close" 
          onClick={onClose}
          aria-label="Close modal"
        >
          ×
        </button>
        <img 
          src={imageSrc} 
          alt={imageAlt} 
          title={imageTitle}
        />
      </div>
    </div>
  )
}

export default ImageModal
