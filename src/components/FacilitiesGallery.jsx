import React, { useState, useEffect } from 'react'

const FacilitiesGallery = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [activeTab, setActiveTab] = useState('all')

  const facilitiesData = [
    {
      category: 'lobby',
      src: 'https://sena.co.th/content/images/2022-12-04/1670363643-nicheskvbearingelegancelobby.jpg',
      alt: 'Elegance Lobby',
      title: 'Elegance Lobby',
      description: 'ล็อบบี้หรูหราสไตล์โมเดิร์น ต้อนรับด้วยการออกแบบที่หรูหราและทันสมัย'
    },
    {
      category: 'lobby',
      src: 'https://sena.co.th/content/images/2022-12-04/1670312592-nicheskvbearingskylounge.jpg',
      alt: 'Sky Lounge',
      title: 'Sky Lounge',
      description: 'ห้องพักผ่อนชั้นบนสุด พร้อมวิวสวยงาม 360 องศา เหมาะสำหรับพักผ่อนและสังสรรค์'
    },
    {
      category: 'lobby',
      src: 'https://sena.co.th/content/images/2022-12-04/1670320979-nicheskvbearingskymeeting.jpg',
      alt: 'Sky Meeting Room',
      title: 'Sky Meeting Room',
      description: 'ห้องประชุมชั้นสูง พร้อมอุปกรณ์ครบครัน เหมาะสำหรับการประชุมและงานสำคัญ'
    },
    {
      category: 'pool',
      src: 'https://sena.co.th/content/images/2022-12-04/1670287134-nicheskvbearingpool.jpg',
      alt: 'Swimming Pool',
      title: 'Swimming Pool',
      description: 'สระว่ายน้ำขนาดใหญ่ในสวนสวย พร้อมพื้นที่พักผ่อนและการออกแบบที่สวยงาม'
    },
    {
      category: 'pool',
      src: 'https://sena.co.th/content/images/2022-12-04/1670412213-nicheskvbearingjoggingtrack.jpg',
      alt: 'Jogging Track',
      title: 'Jogging Track',
      description: 'ลู่วิ่งในสวนสวย ล้อมรอบด้วยธรรมชาติ เหมาะสำหรับการออกกำลังกายในบรรยากาศสบาย'
    },
    {
      category: 'fitness',
      src: 'https://sena.co.th/content/images/2022-12-04/1670290035-nicheskvbearinggym.jpg',
      alt: 'Gymnasium',
      title: 'Gymnasium',
      description: 'ห้องออกกำลังกายพร้อมอุปกรณ์ทันสมัย และวิวกว้างไกล 270 องศา'
    },
    {
      category: 'fitness',
      src: 'https://sena.co.th/content/images/2022-12-04/1670295741-nicheskvbearingboxingroom.jpg',
      alt: 'Boxing Room',
      title: 'Boxing Room',
      description: 'ห้องซ้อมมวยและศิลปะการต่อสู้ พร้อมอุปกรณ์มาตรฐานสำหรับการออกกำลังกายแบบเข้มข้น'
    },
    {
      category: 'fitness',
      src: 'https://sena.co.th/content/images/2022-12-04/1670292076-nicheskvbearingyogaroom.jpg',
      alt: 'Yoga Room',
      title: 'Yoga Room',
      description: 'ห้องโยคะเงียบสงบ เหมาะสำหรับการฝึกโยคะและสมาธิ ในบรรยากาศที่ผ่อนคลาย'
    },
    {
      category: 'lifestyle',
      src: 'https://sena.co.th/content/images/2022-12-04/1670301541-nicheskvbearingcoworkingspace.jpg',
      alt: 'Co-working Space',
      title: 'Co-working Space',
      description: 'พื้นที่ทำงานร่วม พร้อม Wi-Fi ความเร็วสูง เหมาะสำหรับการทำงานและเรียนรู้'
    },
    {
      category: 'lifestyle',
      src: 'https://sena.co.th/content/images/2022-12-04/1670295664-nicheskvbearinggameroom.jpg',
      alt: 'Game Room',
      title: 'Game Room',
      description: 'ห้องเกมส์และบันเทิง พร้อมเกมต่างๆ เหมาะสำหรับพักผ่อนและสร้างความสัมพันธ์'
    },
    {
      category: 'wellness',
      src: 'https://sena.co.th/content/images/2022-12-04/1670360398-nicheskvbearingsaunaroom.jpg',
      alt: 'Sauna Room',
      title: 'Sauna Room',
      description: 'ห้องซาวน่าเพื่อการผ่อนคลาย ช่วยให้ร่างกายและจิตใจสดชื่น หลังการออกกำลังกาย'
    }
  ]

  const filteredSlides = activeTab === 'all' 
    ? facilitiesData 
    : facilitiesData.filter(slide => slide.category === activeTab)

  const tabs = [
    { id: 'all', label: 'ALL' },
    { id: 'lobby', label: 'LOBBY' },
    { id: 'pool', label: 'POOL' },
    { id: 'fitness', label: 'FITNESS' },
    { id: 'lifestyle', label: 'LIFESTYLE' },
    { id: 'wellness', label: 'WELLNESS' }
  ]

  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    setCurrentSlide(0)
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % filteredSlides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => prev === 0 ? filteredSlides.length - 1 : prev - 1)
  }

  const goToSlide = (index) => {
    setCurrentSlide(index)
  }

  return (
    <section id="facilities-gallery" className="section bg-light">
      <div className="container">
        <h2 className="section-title">FACILITIES</h2>
        <div className="facilities-tabs">
          {tabs.map(tab => (
            <button 
              key={tab.id}
              className={`tab-btn ${activeTab === tab.id ? 'active' : ''}`}
              onClick={() => handleTabChange(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        
        <div className="facilities-slider">
          <div className="slider-container">
            <div 
              className="slider-track" 
              id="facilities-track"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {filteredSlides.map((slide, index) => (
                <div key={index} className="facility-slide" data-category={slide.category}>
                  <div className="facility-image">
                    <img src={slide.src} alt={slide.alt} />
                    <div className="facility-overlay">
                      <h4>{slide.title}</h4>
                      <p>{slide.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <button className="slider-btn prev-btn" onClick={prevSlide}>
            <i className="fas fa-chevron-left"></i>
          </button>
          <button className="slider-btn next-btn" onClick={nextSlide}>
            <i className="fas fa-chevron-right"></i>
          </button>
          
          <div className="slider-dots">
            {filteredSlides.map((_, index) => (
              <div 
                key={index}
                className={`dot ${currentSlide === index ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default FacilitiesGallery
