import React, { useState } from 'react'

const UnitType = () => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    console.error('Unit layout image failed to load')
  }

  return (
    <section id="unittype" className="section">
      <div className="container">
        <h2 className="section-title">ประเภทห้อง 33 ตร.ม.</h2>
        <div className="unit-type-grid">
          <div className="unit-layout">
            {!imageLoaded && !imageError && (
              <div style={{ 
                width: '100%', 
                height: '300px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px'
              }}>
                Loading unit layout...
              </div>
            )}
            <img 
              src="https://minio-api.wattanachai.dev/nich-mono/unit-layout.jpg" 
              alt="แปลนห้อง 33 ตร.ม." 
              id="unit-layout-img"
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{ display: imageLoaded ? 'block' : 'none' }}
            />
            {imageError && (
              <div style={{ 
                width: '100%', 
                height: '300px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px',
                color: '#666'
              }}>
                Unit layout image unavailable
              </div>
            )}
          </div>
          <div className="unit-details">
            <h3>ห้องมุม 33 ตร.ม.</h3>
            <div className="features">
              <div className="feature">
                <i className="fas fa-bed"></i>
                <span>1 ห้องนอน</span>
              </div>
              <div className="feature">
                <i className="fas fa-bath"></i>
                <span>1 ห้องน้ำ</span>
              </div>
              <div className="feature">
                <i className="fas fa-utensils"></i>
                <span>Fully furnished</span>
              </div>
              <div className="feature">
                <i className="fas fa-wind"></i>
                <span>ระเบียง ชมวิว จากห้องนั่งเล่น</span>
              </div>
            </div>
            <div className="unit-highlights">
              <h4>จุดเด่น</h4>
              <ul>
                <li>ห้องมุมรับแสงธรรมชาติดี ห้องนอนไม่ติดกับใคร ไม่มีเสียงรบกวน</li>
                <li>วิวเมืองและบางกระเจ้าสวยงาม เห็นแม่น้ำดจ้าพระยา</li>
                <li>การใช้พื้นที่คุ้มค่า</li>
                <li>ระเบียงกว้างขวาง</li>
                <li>ไม่ไกลจากลิฟต์ และบันไดหนีไฟ</li>
                <li>ห้องเจ้าของอยู่เอง ไม่เคยเจาะ หรือผ่านการซ่อมแซมอะไร</li>
                <li>ห้องไม่ได้รับผลกระทบจากเหตุการณ์แผ่นดินไหว</li>
                <li>มีระบบ Key Card และแสกนหน้าเข้าลิฟต์ ป้องกัน Airbnb ได้ดี</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UnitType
