import React, { useState } from 'react'

const Map = () => {
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  const handleImageLoad = () => {
    setImageLoaded(true)
  }

  const handleImageError = () => {
    setImageError(true)
    console.error('Location map image failed to load')
  }

  return (
    <section id="map" className="section bg-light">
      <div className="container">
        <h2 className="section-title">แผนที่และทำเล</h2>
        <div className="map-content">
          <div className="map-container">
            {!imageLoaded && !imageError && (
              <div style={{ 
                width: '100%', 
                height: '400px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px'
              }}>
                Loading map...
              </div>
            )}
            <img 
              src="https://minio-api.wattanachai.dev/nich-mono/location-map.jpg" 
              alt="แผนที่โครงการ" 
              id="location-map"
              onLoad={handleImageLoad}
              onError={handleImageError}
              style={{ display: imageLoaded ? 'block' : 'none' }}
            />
            {imageError && (
              <div style={{ 
                width: '100%', 
                height: '400px', 
                background: '#f0f0f0', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                borderRadius: '15px',
                color: '#666'
              }}>
                Map image unavailable
              </div>
            )}
          </div>
          <div className="location-info">
            <h3>ทำเลยอดเยี่ยม</h3>
            <div className="location-features">
              <div className="location-item">
                <i className="fas fa-train"></i>
                <span>BTS แบริ่ง 300 ม.</span>
              </div>
              <div className="location-item">
                <i className="fas fa-road"></i>
                <span>ติดถนนสุขุมวิท</span>
              </div>
              <div className="location-item">
                <i className="fas fa-car"></i>
                <span>เข้าสู่ทองหล่อ 20 นาที</span>
              </div>
              <div className="location-item">
                <i className="fas fa-plane"></i>
                <span>สนามบินสุวรรณภูมิ 30 นาที</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Map
