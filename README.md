# Niche Mono Sukhumvit - Bearing Condo Listing

A beautiful React-based website for a luxury condo listing in Bangkok, Thailand. This project has been converted from a static HTML website to a modern React application while maintaining 100% visual fidelity.

## 🏢 Project Overview

This is a professional real estate listing website for a 33 sqm corner unit on the 25th floor of Niche Mono Sukhumvit - Bearing condominium. The website features:

- **Hero Section** with stunning building imagery
- **Interactive Gallery** with category filtering and auto-play slider
- **Facilities Showcase** with detailed amenities
- **Floor Plans** and unit layouts
- **Location Information** with nearby amenities
- **Contact Information** for direct owner sales

## ✨ Features

### 🎨 Design & UX
- **100% Visual Fidelity** - Exact replica of original HTML design
- **Responsive Design** - Works perfectly on all devices
- **Smooth Animations** - Beautiful gradients and hover effects
- **Thai Language Support** - Full Thai language content

### 🖼️ Image Handling
- **Improved Image Loading** - Fixed intermittent loading issues
- **Loading States** - Graceful loading indicators
- **Error Handling** - Fallback displays for failed images
- **Lazy Loading** - Optimized performance

### 🎛️ Interactive Components
- **Gallery Slider** - Auto-playing with manual controls
- **Category Filtering** - Filter images by room type
- **Smooth Navigation** - Anchor-based section navigation
- **Hover Effects** - Enhanced user interactions

## 🚀 Quick Start

### Development Mode

```bash
# Navigate to React app directory
cd react-app

# Install dependencies
npm install

# Start development server
npm run dev
```

The development server will start at `http://localhost:5173`

### Production Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Build and run with Docker Compose
docker-compose -f docker-compose-react.yml up --build

# Run in background
docker-compose -f docker-compose-react.yml up -d --build
```

The application will be available at `http://localhost:3000`

### Manual Docker Commands

```bash
# Build the Docker image
docker build -t niche-mono-react ./react-app

# Run the container
docker run -d -p 3000:80 --name niche-mono-app niche-mono-react

# Stop and remove container
docker stop niche-mono-app && docker rm niche-mono-app
```

## 📁 Project Structure

```
niche-mono/
├── src/                      # React source code
│   ├── components/           # React components
│   │   ├── Header.jsx
│   │   ├── Hero.jsx
│   │   ├── Gallery.jsx
│   │   ├── Facilities.jsx
│   │   ├── FacilitiesGallery.jsx
│   │   ├── FloorPlan.jsx
│   │   ├── UnitType.jsx
│   │   ├── Map.jsx
│   │   ├── Nearby.jsx
│   │   ├── Contact.jsx
│   │   ├── Footer.jsx
│   │   └── Information.jsx
│   ├── styles/              # CSS files
│   │   ├── style.css
│   │   ├── facilities-gallery.css
│   │   └── image-fix.css
│   └── App.jsx              # Main app component
├── public/                  # Public assets
├── dist/                    # Production build output
├── Dockerfile               # Docker configuration
├── nginx-react.conf         # Nginx configuration
├── docker-compose.yml       # Docker Compose config
├── package.json             # Dependencies
├── vite.config.js           # Vite configuration
├── index.html               # HTML template
└── README.md                # This file
```

## 🔧 Technical Details

### React Components
- **Header** - Navigation and branding
- **Hero** - Main banner with price and highlights
- **Information** - Project details and specifications
- **FloorPlan** - Interactive floor plan display
- **UnitType** - Unit features and highlights
- **Gallery** - Image gallery with filtering
- **Facilities** - Amenities grid
- **FacilitiesGallery** - Facilities image slider
- **Map** - Location and transportation
- **Nearby** - Nearby places and amenities
- **Contact** - Owner contact information
- **Footer** - Site footer

### Image Loading Improvements
- **Error Handling** - Graceful fallbacks for failed images
- **Loading States** - Visual feedback during image loading
- **Retry Logic** - Automatic retry for failed loads
- **Performance** - Optimized loading strategies

### Docker Configuration
- **Multi-stage Build** - Optimized production builds
- **Nginx Optimization** - Proper caching and compression
- **Security Headers** - Enhanced security configuration
- **Health Checks** - Container health monitoring

## 🌐 Deployment Options

### 1. Docker Compose (Recommended)
- Easy deployment with single command
- Automatic container management
- Production-ready configuration

### 2. Manual Docker
- Full control over container settings
- Custom port configurations
- Advanced networking options

### 3. Static Hosting
- Build and deploy to any static host
- CDN-friendly optimized builds
- Fast global delivery

## 🛠️ Development

### Prerequisites
- Node.js 18+
- Docker (for containerized deployment)
- Modern web browser

### Local Development
1. Clone the repository
2. Navigate to `react-app` directory
3. Run `npm install`
4. Run `npm run dev`
5. Open `http://localhost:5173`

### Building for Production
1. Run `npm run build`
2. Files will be generated in `dist/` directory
3. Deploy `dist/` contents to your web server

## 📱 Browser Support

- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## 🎯 Performance

- **Fast Loading** - Optimized bundle sizes
- **Efficient Caching** - Smart cache strategies
- **Image Optimization** - Compressed and optimized images
- **Code Splitting** - Lazy-loaded components

## 📞 Contact

For inquiries about the property:
- **Phone**: ************
- **Line ID**: templeboy92
- **Email**: <EMAIL>

## 🔄 Migration from HTML to React

This project was successfully migrated from a static HTML website to a modern React application with the following improvements:

### ✅ What Was Preserved
- **100% Visual Design** - Every pixel matches the original
- **All Functionality** - Gallery sliders, navigation, animations
- **Performance** - Actually improved with better image handling
- **SEO Structure** - Maintained semantic HTML structure

### ✅ What Was Improved
- **Image Loading** - Fixed intermittent loading issues
- **Code Organization** - Modular React components
- **Maintainability** - Easier to update and modify
- **Deployment** - Docker containerization for reliable hosting
- **Development Experience** - Hot reload and modern tooling

### ✅ Migration Benefits
- **Reliability** - No more random image loading failures
- **Scalability** - Easy to add new features
- **Modern Stack** - Future-proof technology
- **Better Performance** - Optimized builds and caching

## 🚀 Getting Started (Quick)

1. **Development**:
   ```bash
   cd react-app && npm install && npm run dev
   ```

2. **Production (Docker)**:
   ```bash
   docker-compose -f docker-compose-react.yml up --build
   ```

3. **Access**: Open `http://localhost:3000` (Docker) or `http://localhost:5173` (Dev)

## 📄 License

This project is for the exclusive listing of Niche Mono Sukhumvit - Bearing Unit.
All rights reserved by the property owner.

---

**Note**: This is a private condo listing website, not an official developer website.